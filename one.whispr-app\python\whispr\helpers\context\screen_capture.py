"""
Screen capture functionality for context awareness.

Uses MSS (Multi-Screen Shot) library to capture screenshots from multiple monitors
with support for different capture modes and configurations.
"""

import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image
import mss
import mss.tools


class ScreenCaptureManager:
    """Manages screen capture operations for context awareness."""
    
    def __init__(self):
        """Initialize the screen capture manager."""
        self.logger = logging.getLogger("whispr.context.screen_capture")
        self._sct = None
        self._monitors = []
        self._last_capture_time = 0
        
    def initialize(self) -> bool:
        """Initialize the screen capture system.

        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            # Don't initialize MSS here - create it fresh each time to avoid threading issues
            with mss.mss() as sct:
                self._monitors = sct.monitors
            self.logger.info(f"Screen capture initialized with {len(self._monitors)} monitors")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize screen capture: {e}")
            return False
    
    def cleanup(self) -> None:
        """Clean up screen capture resources."""
        # No persistent MSS instance to clean up
        self._monitors = []
        self.logger.debug("Screen capture resources cleaned up")
    
    def get_monitors(self) -> List[Dict[str, Any]]:
        """Get information about available monitors.
        
        Returns:
            List of monitor information dictionaries
        """
        if not self._sct:
            return []
            
        monitors = []
        for i, monitor in enumerate(self._monitors):
            if i == 0:  # Skip the "All in One" monitor
                continue
                
            monitors.append({
                "index": i,
                "left": monitor["left"],
                "top": monitor["top"], 
                "width": monitor["width"],
                "height": monitor["height"],
                "is_primary": i == 1  # First real monitor is usually primary
            })
        
        return monitors
    
    def capture_screen(self, mode: str = "allMonitors", max_size: Optional[Tuple[int, int]] = None) -> Optional[Image.Image]:
        """Capture a screenshot of all monitors for full context.

        Args:
            mode: Capture mode (always captures all monitors for full context)
            max_size: Optional maximum size (width, height) to resize the image

        Returns:
            PIL Image object or None if capture failed
        """
        if not self._monitors:
            self.logger.error("Screen capture not initialized")
            return None

        try:
            self._last_capture_time = time.time()

            # Always capture all monitors for full context
            return self._capture_all_monitors(max_size)

        except Exception as e:
            self.logger.error(f"Error capturing screen: {e}")
            return None
    
    def _capture_primary_monitor(self, max_size: Optional[Tuple[int, int]] = None) -> Optional[Image.Image]:
        """Capture the primary monitor.
        
        Args:
            max_size: Optional maximum size to resize the image
            
        Returns:
            PIL Image object or None if capture failed
        """
        try:
            # Monitor 1 is usually the primary monitor (0 is "All in One")
            monitor = self._monitors[1] if len(self._monitors) > 1 else self._monitors[0]
            
            screenshot = self._sct.grab(monitor)
            image = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            
            if max_size:
                image = self._resize_image(image, max_size)
                
            self.logger.debug(f"Captured primary monitor: {image.size}")
            return image
            
        except Exception as e:
            self.logger.error(f"Error capturing primary monitor: {e}")
            return None
    
    def _capture_all_monitors(self, max_size: Optional[Tuple[int, int]] = None) -> Optional[Image.Image]:
        """Capture all monitors as a single image.
        
        Args:
            max_size: Optional maximum size to resize the image
            
        Returns:
            PIL Image object or None if capture failed
        """
        try:
            # Use monitor 0 which captures all monitors
            monitor = self._monitors[0]
            
            screenshot = self._sct.grab(monitor)
            image = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            
            if max_size:
                image = self._resize_image(image, max_size)
                
            self.logger.debug(f"Captured all monitors: {image.size}")
            return image
            
        except Exception as e:
            self.logger.error(f"Error capturing all monitors: {e}")
            return None
    
    def _capture_active_window(self, max_size: Optional[Tuple[int, int]] = None) -> Optional[Image.Image]:
        """Capture the active window.
        
        Note: This is a simplified implementation that captures the primary monitor.
        A more advanced implementation would use Windows API to get the active window bounds.
        
        Args:
            max_size: Optional maximum size to resize the image
            
        Returns:
            PIL Image object or None if capture failed
        """
        # For now, fall back to primary monitor capture
        # TODO: Implement proper active window detection using pywin32
        self.logger.debug("Active window capture not fully implemented, using primary monitor")
        return self._capture_primary_monitor(max_size)
    
    def _resize_image(self, image: Image.Image, max_size: Tuple[int, int]) -> Image.Image:
        """Resize an image while maintaining aspect ratio.
        
        Args:
            image: The PIL Image to resize
            max_size: Maximum size (width, height)
            
        Returns:
            Resized PIL Image
        """
        try:
            max_width, max_height = max_size
            
            # Calculate the scaling factor
            width_ratio = max_width / image.width
            height_ratio = max_height / image.height
            scale_factor = min(width_ratio, height_ratio, 1.0)  # Don't upscale
            
            if scale_factor < 1.0:
                new_width = int(image.width * scale_factor)
                new_height = int(image.height * scale_factor)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                self.logger.debug(f"Resized image to {new_width}x{new_height}")
            
            return image
            
        except Exception as e:
            self.logger.error(f"Error resizing image: {e}")
            return image
    
    def get_last_capture_time(self) -> float:
        """Get the timestamp of the last capture.
        
        Returns:
            Unix timestamp of the last capture
        """
        return self._last_capture_time
    
    def is_initialized(self) -> bool:
        """Check if the screen capture system is initialized.
        
        Returns:
            True if initialized, False otherwise
        """
        return self._sct is not None
