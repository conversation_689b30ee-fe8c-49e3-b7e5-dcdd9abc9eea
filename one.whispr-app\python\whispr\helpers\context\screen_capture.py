"""
Screen capture functionality for context awareness.

Uses MSS (Multi-Screen Shot) library to capture screenshots from multiple monitors
with support for different capture modes and configurations.
"""

import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image
import mss
import mss.tools


class ScreenCaptureManager:
    """Manages screen capture operations for context awareness."""
    
    def __init__(self):
        """Initialize the screen capture manager."""
        self.logger = logging.getLogger("whispr.context.screen_capture")
        self._sct = None
        self._monitors = []
        self._last_capture_time = 0
        
    def initialize(self) -> bool:
        """Initialize the screen capture system.

        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            # Don't initialize MSS here - create it fresh each time to avoid threading issues
            with mss.mss() as sct:
                self._monitors = sct.monitors
            self.logger.info(f"Screen capture initialized with {len(self._monitors)} monitors")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize screen capture: {e}")
            return False
    
    def cleanup(self) -> None:
        """Clean up screen capture resources."""
        # No persistent MSS instance to clean up
        self._monitors = []
        self.logger.debug("Screen capture resources cleaned up")
    
    def get_monitors(self) -> List[Dict[str, Any]]:
        """Get information about available monitors.
        
        Returns:
            List of monitor information dictionaries
        """
        if not self._sct:
            return []
            
        monitors = []
        for i, monitor in enumerate(self._monitors):
            if i == 0:  # Skip the "All in One" monitor
                continue
                
            monitors.append({
                "index": i,
                "left": monitor["left"],
                "top": monitor["top"], 
                "width": monitor["width"],
                "height": monitor["height"],
                "is_primary": i == 1  # First real monitor is usually primary
            })
        
        return monitors
    
    def capture_screen(self) -> Optional[Image.Image]:
        """Capture a screenshot of all monitors at native resolution.

        Returns:
            PIL Image object or None if capture failed
        """
        if not self._monitors:
            self.logger.error("Screen capture not initialized")
            return None

        try:
            self._last_capture_time = time.time()

            # Always capture all monitors for full context at native resolution
            return self._capture_all_monitors()

        except Exception as e:
            self.logger.error(f"Error capturing screen: {e}")
            return None
    

    
    def _capture_all_monitors(self) -> Optional[Image.Image]:
        """Capture all monitors as a single image at native resolution.

        Returns:
            PIL Image object or None if capture failed
        """
        try:
            # Create fresh MSS instance to avoid threading issues
            with mss.mss() as sct:
                # Use monitor 0 which captures all monitors
                monitor = self._monitors[0]

                screenshot = sct.grab(monitor)
                image = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")

                self.logger.debug(f"Captured all monitors at native resolution: {image.size}")
                return image

        except Exception as e:
            self.logger.error(f"Error capturing all monitors: {e}")
            return None
    

    
    def get_last_capture_time(self) -> float:
        """Get the timestamp of the last capture.
        
        Returns:
            Unix timestamp of the last capture
        """
        return self._last_capture_time
    
    def is_initialized(self) -> bool:
        """Check if the screen capture system is initialized.

        Returns:
            True if initialized, False otherwise
        """
        return len(self._monitors) > 0
