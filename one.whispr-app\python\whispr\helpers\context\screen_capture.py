"""
Screen capture functionality for context awareness.

Uses MSS (Multi-Screen Shot) library to capture screenshots from multiple monitors
with support for different capture modes and configurations.
"""

import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image
import mss
import mss.tools


class ScreenCaptureManager:
    """Manages screen capture operations for context awareness."""
    
    def __init__(self):
        """Initialize the screen capture manager."""
        self.logger = logging.getLogger("whispr.context.screen_capture")
        self._sct = None
        self._monitors = []
        self._last_capture_time = 0
        
    def initialize(self) -> bool:
        """Initialize the screen capture system.

        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            # Don't initialize MSS here - create it fresh each time to avoid threading issues
            with mss.mss() as sct:
                self._monitors = sct.monitors
            self.logger.info(f"Screen capture initialized with {len(self._monitors)} monitors")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize screen capture: {e}")
            return False
    
    def cleanup(self) -> None:
        """Clean up screen capture resources."""
        # No persistent MSS instance to clean up
        self._monitors = []
        self.logger.debug("Screen capture resources cleaned up")
    
    def get_monitors(self) -> List[Dict[str, Any]]:
        """Get information about available monitors.
        
        Returns:
            List of monitor information dictionaries
        """
        if not self._sct:
            return []
            
        monitors = []
        for i, monitor in enumerate(self._monitors):
            if i == 0:  # Skip the "All in One" monitor
                continue
                
            monitors.append({
                "index": i,
                "left": monitor["left"],
                "top": monitor["top"], 
                "width": monitor["width"],
                "height": monitor["height"],
                "is_primary": i == 1  # First real monitor is usually primary
            })
        
        return monitors
    
    def capture_screen(self) -> Optional[Image.Image]:
        """Capture a screenshot of the active window for better performance.

        Returns:
            PIL Image object or None if capture failed
        """
        if not self._monitors:
            self.logger.error("Screen capture not initialized")
            return None

        try:
            self._last_capture_time = time.time()

            # Try to capture active window first (much faster)
            active_window_image = self._capture_active_window()
            if active_window_image:
                return active_window_image

            # Fall back to primary monitor if active window capture fails
            return self._capture_primary_monitor()

        except Exception as e:
            self.logger.error(f"Error capturing screen: {e}")
            return None
    

    
    def _capture_all_monitors(self) -> Optional[Image.Image]:
        """Capture all monitors as a single image at native resolution.

        Returns:
            PIL Image object or None if capture failed
        """
        try:
            # Create fresh MSS instance to avoid threading issues
            with mss.mss() as sct:
                # Use monitor 0 which captures all monitors
                monitor = self._monitors[0]

                screenshot = sct.grab(monitor)
                image = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")

                self.logger.debug(f"Captured all monitors at native resolution: {image.size}")
                return image

        except Exception as e:
            self.logger.error(f"Error capturing all monitors: {e}")
            return None
    

    
    def get_last_capture_time(self) -> float:
        """Get the timestamp of the last capture.
        
        Returns:
            Unix timestamp of the last capture
        """
        return self._last_capture_time
    
    def is_initialized(self) -> bool:
        """Check if the screen capture system is initialized.

        Returns:
            True if initialized, False otherwise
        """
        return len(self._monitors) > 0

    def _capture_active_window(self) -> Optional[Image.Image]:
        """Capture the active window using Windows API.

        Returns:
            PIL Image object or None if capture failed
        """
        try:
            import win32gui
            import win32ui
            import win32con

            # Get the active window
            hwnd = win32gui.GetForegroundWindow()
            if not hwnd:
                self.logger.debug("No active window found")
                return None

            # Get window dimensions
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            width = right - left
            height = bottom - top

            # Skip very small windows or invalid dimensions
            if width < 100 or height < 100 or width > 4000 or height > 4000:
                self.logger.debug(f"Window size invalid ({width}x{height}), skipping")
                return None

            # Create device context
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()

            # Create bitmap
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)

            # Copy window content with error handling
            try:
                result = saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
            except Exception as e:
                self.logger.debug(f"BitBlt failed: {e}")
                result = 0

            if result:
                # Convert to PIL Image
                bmpinfo = saveBitMap.GetInfo()
                bmpstr = saveBitMap.GetBitmapBits(True)

                image = Image.frombuffer(
                    'RGB',
                    (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                    bmpstr, 'raw', 'BGRX', 0, 1
                )

                self.logger.debug(f"Captured active window: {width}x{height}")

                # Clean up
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)

                return image
            else:
                self.logger.debug("Failed to capture window content")
                return None

        except ImportError:
            self.logger.debug("pywin32 not available for active window capture")
            return None
        except Exception as e:
            self.logger.debug(f"Error capturing active window: {e}")
            return None
        finally:
            # Ensure cleanup even if error occurs
            try:
                if 'saveBitMap' in locals():
                    win32gui.DeleteObject(saveBitMap.GetHandle())
                if 'saveDC' in locals():
                    saveDC.DeleteDC()
                if 'mfcDC' in locals():
                    mfcDC.DeleteDC()
                if 'hwndDC' in locals() and 'hwnd' in locals():
                    win32gui.ReleaseDC(hwnd, hwndDC)
            except:
                pass

    def _capture_primary_monitor(self) -> Optional[Image.Image]:
        """Capture the primary monitor as fallback.

        Returns:
            PIL Image object or None if capture failed
        """
        try:
            # Create fresh MSS instance to avoid threading issues
            with mss.mss() as sct:
                # Use monitor 1 (primary monitor)
                monitor = self._monitors[1] if len(self._monitors) > 1 else self._monitors[0]

                screenshot = sct.grab(monitor)
                image = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")

                self.logger.debug(f"Captured primary monitor: {image.size}")
                return image

        except Exception as e:
            self.logger.error(f"Error capturing primary monitor: {e}")
            return None
