 DEBUG - Extracted 1 text elements
[Backend] 2025-07-09 23:00:54,750 - whispr.ContextService - INFO - OCR extracted 1 words (confidence: 1.00): 'Messages'
2025-07-09 23:00:54,750 - whispr.ContextService - DEBUG - High confidence words: ['Messages']
[Backend] 2025-07-09 23:00:54,753 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.32
2025-07-09 23:00:54,753 - whispr.ContextService - INFO - Active window: 'context_service.py - one.whispr - Cursor'
2025-07-09 23:00:54,753 - whispr.ContextService - INFO - Top keywords: ['messages (0.60)']
[Backend] 2025-07-09 23:00:54,753 - whispr.ContextService - INFO - Context relevance score: 0.32
2025-07-09 23:00:54,753 - whispr.context.cache - DEBUG - Stored context in cache: 2c4b63a323736557399cbf14c7d1732e
2025-07-09 23:00:54,753 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.294s, OCR: 2.687s, Analysis: 0.003s
[Backend] 2025-07-09 23:00:54,754 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.32, processing time: 2.99s
[Backend] 2025-07-09 23:00:56,763 - whispr.ContextService - INFO - Context capture triggered: fallback timer
2025-07-09 23:00:56,763 - whispr.ContextService - DEBUG - Context capture skipped - too recent
[Backend] 2025-07-09 23:01:00,790 - whispr.ContextService - INFO - Context capture triggered: app change: 'Cursor' -> 'Discord'
2025-07-09 23:01:00,790 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:01:00,793 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:01:00,930 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:01:00,938 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:01:00,943 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:01:03,405 - whispr.context.ocr - DEBUG - Extracted 1 text elements
[Backend] 2025-07-09 23:01:03,405 - whispr.ContextService - INFO - OCR extracted 1 words (confidence: 1.00): 'Messages'
2025-07-09 23:01:03,406 - whispr.ContextService - DEBUG - High confidence words: ['Messages']
[Backend] 2025-07-09 23:01:03,406 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.59
2025-07-09 23:01:03,406 - whispr.ContextService - INFO - Active window: '#general | Suck Your Mum - Discord'
2025-07-09 23:01:03,406 - whispr.ContextService - INFO - Detected applications: ['Discord (0.90)']
2025-07-09 23:01:03,406 - whispr.ContextService - INFO - Top keywords: ['messages (0.60)']
2025-07-09 23:01:03,406 - whispr.ContextService - INFO - Context relevance score: 0.59
2025-07-09 23:01:03,406 - whispr.context.cache - DEBUG - Stored context in cache: 954610b71932980ac8b810f5286089c1
[Backend] 2025-07-09 23:01:03,406 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.150s, OCR: 2.466s, Analysis: 0.000s
2025-07-09 23:01:03,407 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.59, processing time: 2.62s
[Backend] 2025-07-09 23:01:13,416 - whispr.ContextService - INFO - Context capture triggered: fallback timer
2025-07-09 23:01:13,416 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:01:13,421 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:01:13,564 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:01:13,572 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:01:13,578 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:01:16,457 - whispr.context.ocr - DEBUG - Extracted 1 text elements
[Backend] 2025-07-09 23:01:16,458 - whispr.ContextService - INFO - OCR extracted 1 words (confidence: 1.00): 'Messages'
2025-07-09 23:01:16,458 - whispr.ContextService - DEBUG - High confidence words: ['Messages']
[Backend] 2025-07-09 23:01:16,458 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.59
2025-07-09 23:01:16,458 - whispr.ContextService - INFO - Active window: '#general | Suck Your Mum - Discord'
2025-07-09 23:01:16,458 - whispr.ContextService - INFO - Detected applications: ['Discord (0.90)']
2025-07-09 23:01:16,459 - whispr.ContextService - INFO - Top keywords: ['messages (0.60)']
[Backend] 2025-07-09 23:01:16,459 - whispr.ContextService - INFO - Context relevance score: 0.59
2025-07-09 23:01:16,459 - whispr.context.cache - DEBUG - Context already cached: 954610b71932980ac8b810f5286089c1
2025-07-09 23:01:16,459 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.159s, OCR: 2.883s, Analysis: 0.000s
[Backend] 2025-07-09 23:01:16,459 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.59, processing time: 3.04s
[Backend] 2025-07-09 23:01:20,529 - whispr.ContextService - INFO - Context capture triggered: app change: 'Discord' -> 'Cursor'
[Backend] 2025-07-09 23:01:20,529 - whispr.ContextService - DEBUG - Starting context capture
2025-07-09 23:01:20,548 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:01:20,788 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:01:20,810 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:01:20,815 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:01:24,054 - whispr.context.ocr - DEBUG - Extracted 1 text elements
[Backend] 2025-07-09 23:01:24,054 - whispr.ContextService - INFO - OCR extracted 1 words (confidence: 1.00): 'Messages'
2025-07-09 23:01:24,054 - whispr.ContextService - DEBUG - High confidence words: ['Messages']
[Backend] 2025-07-09 23:01:24,055 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.32
2025-07-09 23:01:24,055 - whispr.ContextService - INFO - Active window: 'context_service.py - one.whispr - Cursor'
[Backend] 2025-07-09 23:01:24,055 - whispr.ContextService - INFO - Top keywords: ['messages (0.60)']
2025-07-09 23:01:24,055 - whispr.ContextService - INFO - Context relevance score: 0.32
2025-07-09 23:01:24,055 - whispr.context.cache - DEBUG - Stored context in cache: 2a71c7a30896481a60df0d16a505eeb5
2025-07-09 23:01:24,055 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.282s, OCR: 3.242s, Analysis: 0.001s
[Backend] 2025-07-09 23:01:24,056 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.32, processing time: 3.53s
[Backend] 2025-07-09 23:01:30,091 - whispr.ContextService - INFO - Context capture triggered: fallback timer
2025-07-09 23:01:30,091 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:01:30,104 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:01:30,360 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:01:30,370 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:01:30,405 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:01:33,273 - whispr.context.ocr - DEBUG - Extracted 3 text elements
[Backend] 2025-07-09 23:01:33,273 - whispr.ContextService - INFO - OCR extracted 3 words (confidence: 0.57): 'Trean 5= Messages'
2025-07-09 23:01:33,273 - whispr.ContextService - DEBUG - High confidence words: ['Messages']
2025-07-09 23:01:33,274 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.24
[Backend] 2025-07-09 23:01:33,274 - whispr.ContextService - INFO - Active window: 'context_service.py - one.whispr - Cursor'
2025-07-09 23:01:33,274 - whispr.ContextService - INFO - Top keywords: ['messages (0.60)', 'trean (0.50)']
2025-07-09 23:01:33,274 - whispr.ContextService - DEBUG - Entities (numbers): ['5']
2025-07-09 23:01:33,274 - whispr.ContextService - INFO - Context relevance score: 0.24
2025-07-09 23:01:33,274 - whispr.context.cache - DEBUG - Stored context in cache: a4720c1d2cd17c61944debce3c4ed02f
2025-07-09 23:01:33,274 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.282s, OCR: 2.901s, Analysis: 0.001s
[Backend] 2025-07-09 23:01:33,275 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.24, processing time: 3.18s
[Backend] 2025-07-09 23:01:45,315 - whispr.ContextService - INFO - Context capture triggered: fallback timer
2025-07-09 23:01:45,315 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:01:45,326 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:01:45,471 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:01:45,481 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:01:45,486 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:01:46,872 - whispr.helpers.models.memory_manager - DEBUG - Moderate memory pressure - performing light cleanup
[Backend] 2025-07-09 23:01:46,884 - whispr.helpers.models.hardware_detector - INFO - PyTorch is available
[Backend] 2025-07-09 23:01:46,897 - whispr.helpers.models.memory_manager - DEBUG - Starting memory cleanup (aggressive=False, light=True)
[Backend] 2025-07-09 23:01:47,089 - whispr.helpers.models.memory_manager - DEBUG - Python GC freed 231 objects
[Backend] 2025-07-09 23:01:47,192 - whispr.helpers.models.memory_manager - DEBUG - Memory cleanup completed in 0.32s, freed ~0.0MB
[Backend] 2025-07-09 23:01:48,865 - whispr.context.ocr - DEBUG - Extracted 1 text elements
[Backend] 2025-07-09 23:01:48,865 - whispr.ContextService - INFO - OCR extracted 1 words (confidence: 1.00): 'Messages'
2025-07-09 23:01:48,865 - whispr.ContextService - DEBUG - High confidence words: ['Messages']
[Backend] 2025-07-09 23:01:48,866 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.32
2025-07-09 23:01:48,866 - whispr.ContextService - INFO - Active window: 'context_service.py - one.whispr - Cursor'
[Backend] 2025-07-09 23:01:48,866 - whispr.ContextService - INFO - Top keywords: ['messages (0.60)']
2025-07-09 23:01:48,866 - whispr.ContextService - INFO - Context relevance score: 0.32
2025-07-09 23:01:48,866 - whispr.context.cache - DEBUG - Context already cached: 2a71c7a30896481a60df0d16a505eeb5
2025-07-09 23:01:48,866 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.168s, OCR: 3.383s, Analysis: 0.000s
[Backend] 2025-07-09 23:01:48,867 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.32, processing time: 3.55s
[Backend] 2025-07-09 23:01:54,885 - whispr.ContextService - INFO - Context capture triggered: app change: 'Cursor' -> 'Discord'
[Backend] 2025-07-09 23:01:54,886 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:01:54,909 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:01:55,154 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:01:55,163 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:01:55,170 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:02:00,999 - whispr.context.ocr - DEBUG - Extracted 2 text elements
[Backend] 2025-07-09 23:02:00,999 - whispr.ContextService - INFO - OCR extracted 2 words (confidence: 0.68): 'Et Messages'
2025-07-09 23:02:00,999 - whispr.ContextService - DEBUG - High confidence words: ['Messages']
[Backend] 2025-07-09 23:02:01,000 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.26
2025-07-09 23:02:01,000 - whispr.ContextService - INFO - Active window: 'Home / X - Opera'
[Backend] 2025-07-09 23:02:01,000 - whispr.ContextService - INFO - Top keywords: ['messages (0.60)']
2025-07-09 23:02:01,000 - whispr.ContextService - INFO - Context relevance score: 0.26
2025-07-09 23:02:01,000 - whispr.context.cache - DEBUG - Stored context in cache: 0967b53372264d4bfd050536abc25784
2025-07-09 23:02:01,000 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.281s, OCR: 5.833s, Analysis: 0.001s
[Backend] 2025-07-09 23:02:01,000 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.26, processing time: 6.11s
[Backend] 2025-07-09 23:02:02,987 - whispr.ContextService - INFO - Context capture triggered: app change: 'Discord' -> 'Opera'
2025-07-09 23:02:02,987 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:02:03,145 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:02:03,293 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:02:03,308 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:02:03,326 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:02:06,695 - whispr.context.ocr - DEBUG - Extracted 12 text elements
[Backend] 2025-07-09 23:02:06,695 - whispr.ContextService - INFO - OCR extracted 12 words (confidence: 0.63): 'Home_ Liva on X Explon Notilications Grok Jcks Aronymoiis Verilied Orgs Protile Who to follow tneme Messages'
2025-07-09 23:02:06,696 - whispr.ContextService - DEBUG - High confidence words: ['Grok', 'Verilied Orgs', 'Messages']
[Backend] 2025-07-09 23:02:06,696 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.23
2025-07-09 23:02:06,696 - whispr.ContextService - INFO - Active window: 'Home / X - Opera'
[Backend] 2025-07-09 23:02:06,697 - whispr.ContextService - INFO - Top keywords: ['notilications (0.60)', 'aronymoiis (0.60)', 'verilied (0.60)', 'protile (0.60)', 'messages (0.60)']
2025-07-09 23:02:06,697 - whispr.ContextService - INFO - Context relevance score: 0.23
[Backend] 2025-07-09 23:02:06,697 - whispr.context.cache - DEBUG - Stored context in cache: 2f5a66f40c03b6139275f54a2275df8d
2025-07-09 23:02:06,697 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.336s, OCR: 3.371s, Analysis: 0.000s
[Backend] 2025-07-09 23:02:06,698 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.23, processing time: 3.71s
[Backend] 2025-07-09 23:02:08,708 - whispr.ContextService - INFO - Context capture triggered: fallback timer
2025-07-09 23:02:08,708 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:02:08,725 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:02:08,864 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:02:08,878 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:02:08,884 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:02:11,478 - whispr.context.ocr - DEBUG - Extracted 10 text elements
[Backend] 2025-07-09 23:02:11,479 - whispr.ContextService - INFO - OCR extracted 10 words (confidence: 0.68): 'Home_ Live on X Explon Notilications Grok Jcks Verilied Orgs Protile Who to follow Messages'
2025-07-09 23:02:11,479 - whispr.ContextService - DEBUG - High confidence words: ['Grok', 'Verilied Orgs', 'Messages']
[Backend] 2025-07-09 23:02:11,480 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.23
[Backend] 2025-07-09 23:02:11,480 - whispr.ContextService - INFO - Active window: 'Home / X - Opera'
2025-07-09 23:02:11,480 - whispr.ContextService - INFO - Top keywords: ['notilications (0.60)', 'verilied (0.60)', 'protile (0.60)', 'messages (0.60)', 'explon (0.50)']
2025-07-09 23:02:11,480 - whispr.ContextService - INFO - Context relevance score: 0.23
2025-07-09 23:02:11,480 - whispr.context.cache - DEBUG - Stored context in cache: 7799b982d88900ea9e092af5dc97c7ea
2025-07-09 23:02:11,480 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.173s, OCR: 2.598s, Analysis: 0.001s
[Backend] 2025-07-09 23:02:11,481 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.23, processing time: 2.77s
[Backend] 2025-07-09 23:02:25,543 - whispr.ContextService - INFO - Context capture triggered: fallback timer
[Backend] 2025-07-09 23:02:25,543 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:02:25,563 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:02:25,708 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:02:25,716 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:02:25,720 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:02:29,125 - whispr.context.ocr - DEBUG - Extracted 20 text elements
[Backend] 2025-07-09 23:02:29,125 - whispr.ContextService - INFO - OCR extracted 20 words (confidence: 0.69): 'Ancicrth story Enthuciacts Home_ Explon on X Notilications plrat Nadon Jcks Lomcs Verilied Orgs Protile Valve "cracked the problem that Netflix was struggling with" using diehard gamers and their back...'
2025-07-09 23:02:29,126 - whispr.ContextService - DEBUG - High confidence words: ['Verilied Orgs', 'Netflix was', 'struggling with" using', 'diehard gamers and their backlogs_', "'You get access", 'spend money irresponsibly"', 'Messages']
[Backend] 2025-07-09 23:02:29,127 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.25
2025-07-09 23:02:29,127 - whispr.ContextService - INFO - Active window: 'Home / X - Opera'
[Backend] 2025-07-09 23:02:29,127 - whispr.ContextService - INFO - Top keywords: ['ancicrth (0.60)', 'enthuciacts (0.60)', 'notilications (0.60)', 'verilied (0.60)', 'protile (0.60)']
2025-07-09 23:02:29,127 - whispr.ContextService - INFO - Context relevance score: 0.25
2025-07-09 23:02:29,127 - whispr.context.cache - DEBUG - Stored context in cache: 18fe5c233caa82d12a9a4ab0465483b9
2025-07-09 23:02:29,127 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.174s, OCR: 3.409s, Analysis: 0.001s
[Backend] 2025-07-09 23:02:29,128 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.25, processing time: 3.59s
[Backend] 2025-07-09 23:02:41,211 - whispr.ContextService - INFO - Context capture triggered: fallback timer
2025-07-09 23:02:41,211 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:02:41,250 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:02:41,658 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:02:41,666 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:02:41,674 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:02:44,680 - whispr.context.ocr - DEBUG - Extracted 12 text elements
[Backend] 2025-07-09 23:02:44,681 - whispr.ContextService - INFO - OCR extracted 12 words (confidence: 0.62): 'Pobt Hame Relevant people Explon copled content Notilications What happening Jcks Verilied Orgs Protile Mare Messages'
2025-07-09 23:02:44,681 - whispr.ContextService - DEBUG - High confidence words: ['Verilied Orgs', 'Messages']
2025-07-09 23:02:44,682 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.22
2025-07-09 23:02:44,682 - whispr.ContextService - INFO - Active window: 'FearBuck on X: "YouTube will only pay creators who use their real voice and produce original content starting July 15, making reused videos, copied content, low-effort uploads, and fully AI-generated videos will be ineligible for monetization https://t.co/8rKCFVrCID" / X - Opera'     
2025-07-09 23:02:44,682 - whispr.ContextService - INFO - Top keywords: ['relevant (0.60)', 'notilications (0.60)', 'verilied (0.60)', 'protile (0.60)', 'messages (0.60)']
2025-07-09 23:02:44,682 - whispr.ContextService - INFO - Context relevance score: 0.22
2025-07-09 23:02:44,682 - whispr.context.cache - DEBUG - Stored context in cache: 55df92f0baac715e96d1b71263c4c25b
2025-07-09 23:02:44,682 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.455s, OCR: 3.013s, Analysis: 0.001s
[Backend] 2025-07-09 23:02:44,683 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.22, processing time: 3.47s
[Backend] 2025-07-09 23:02:46,678 - whispr.ContextService - INFO - Context capture triggered: content change: 'Home / X' -> 'FearBuck on X: "YouTube will only pay creators who use their real voice and produce original content starting July 15, making reused videos, copied content, low-effort uploads, and fully AI-generated videos will be ineligible for monetization https://t.co/8rKCFVrCID" / X'
2025-07-09 23:02:46,678 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:02:46,693 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:02:46,826 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:02:46,834 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:02:46,839 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:02:47,286 - whispr.helpers.models.memory_manager - DEBUG - Moderate memory pressure - performing light cleanup
[Backend] 2025-07-09 23:02:47,292 - whispr.helpers.models.memory_manager - DEBUG - Starting memory cleanup (aggressive=False, light=True)
[Backend] 2025-07-09 23:02:47,395 - whispr.helpers.models.memory_manager - DEBUG - Python GC freed 256 objects
[Backend] 2025-07-09 23:02:47,505 - whispr.helpers.models.memory_manager - DEBUG - Memory cleanup completed in 0.22s, freed ~0.0MB
[Backend] 2025-07-09 23:02:50,598 - whispr.context.ocr - DEBUG - Extracted 16 text elements
[Backend] 2025-07-09 23:02:50,598 - whispr.ContextService - INFO - OCR extracted 16 words (confidence: 0.57): 'post Hame Relevant people Explon You Tube) Notilications Qx: What happening Jcks Verilied Orgs Protile Nctexacdy Update our long-standing YFP igible Messages'
2025-07-09 23:02:50,599 - whispr.ContextService - DEBUG - High confidence words: ['Verilied Orgs', 'our long-standing YFP', 'Messages']
[Backend] 2025-07-09 23:02:50,599 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.26
2025-07-09 23:02:50,599 - whispr.ContextService - INFO - Active window: 'FearBuck on X: "YouTube will only pay creators who use their real voice and produce original content starting July 15, making reused videos, copied content, low-effort uploads, and fully AI-generated videos will be ineligible for monetization https://t.co/8rKCFVrCID" / X - Opera'     
[Backend] 2025-07-09 23:02:50,600 - whispr.ContextService - INFO - Top keywords: ['relevant (0.60)', 'notilications (0.60)', 'verilied (0.60)', 'protile (0.60)', 'nctexacdy (0.60)']
2025-07-09 23:02:50,600 - whispr.ContextService - DEBUG - Technical terms (database): ['update']
2025-07-09 23:02:50,600 - whispr.ContextService - INFO - Context relevance score: 0.26
2025-07-09 23:02:50,600 - whispr.context.cache - DEBUG - Stored context in cache: c1af410210311087b6406f140c8f5940
2025-07-09 23:02:50,600 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.159s, OCR: 3.763s, Analysis: 0.000s
[Backend] 2025-07-09 23:02:50,600 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.26, processing time: 3.92s
[Backend] 2025-07-09 23:02:56,609 - whispr.ContextService - INFO - Context capture triggered: fallback timer
2025-07-09 23:02:56,609 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:02:56,642 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:02:56,792 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:02:56,802 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:02:56,810 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:03:03,319 - whispr.context.ocr - DEBUG - Extracted 16 text elements
[Backend] 2025-07-09 23:03:03,320 - whispr.ContextService - INFO - OCR extracted 16 words (confidence: 0.57): 'post Hame Relevant people Explon You Tube) Notilications Qx: What happening Jcks Verilied Orgs Protile Update our long-standing YFP Thi; LyF igible Messages'
2025-07-09 23:03:03,320 - whispr.ContextService - DEBUG - High confidence words: ['Verilied Orgs', 'our long-standing YFP', 'Messages']
[Backend] 2025-07-09 23:03:03,321 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.53
2025-07-09 23:03:03,322 - whispr.ContextService - INFO - Active window: '#general | Suck Your Mum - Discord'
2025-07-09 23:03:03,322 - whispr.ContextService - INFO - Detected applications: ['Discord (0.90)']
2025-07-09 23:03:03,322 - whispr.ContextService - INFO - Top keywords: ['relevant (0.60)', 'notilications (0.60)', 'verilied (0.60)', 'protile (0.60)', 'messages (0.60)']
2025-07-09 23:03:03,322 - whispr.ContextService - DEBUG - Technical terms (database): ['update']
2025-07-09 23:03:03,322 - whispr.ContextService - INFO - Context relevance score: 0.53
[Backend] 2025-07-09 23:03:03,322 - whispr.context.cache - DEBUG - Stored context in cache: 217b93fd94d8640261a60272966c446d
2025-07-09 23:03:03,324 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.195s, OCR: 6.517s, Analysis: 0.002s
[Backend] 2025-07-09 23:03:03,326 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.53, processing time: 6.72s
[Backend] 2025-07-09 23:03:13,353 - whispr.ContextService - INFO - Context capture triggered: fallback timer
2025-07-09 23:03:13,353 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:03:13,367 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:03:13,513 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:03:13,521 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:03:13,527 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:03:17,603 - whispr.context.ocr - DEBUG - Extracted 0 text elements
[Backend] 2025-07-09 23:03:17,603 - whispr.ContextService - DEBUG - No text extracted from screenshot
[Backend] 2025-07-09 23:03:17,604 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.00
2025-07-09 23:03:17,604 - whispr.ContextService - INFO - Active window: 'context_service.py - one.whispr - Cursor'
[Backend] 2025-07-09 23:03:17,604 - whispr.ContextService - INFO - Context relevance score: 0.00
2025-07-09 23:03:17,604 - whispr.context.cache - DEBUG - Stored context in cache: ed8429ce80db0be896a5af1c53033224
2025-07-09 23:03:17,604 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.171s, OCR: 4.078s, Analysis: 0.001s
[Backend] 2025-07-09 23:03:17,605 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.00, processing time: 4.25s
[Backend] 2025-07-09 23:03:19,615 - whispr.ContextService - INFO - Context capture triggered: app change: 'Opera' -> 'Cursor'
2025-07-09 23:03:19,615 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:03:19,625 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:03:19,754 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:03:19,763 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:03:19,768 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:03:23,749 - whispr.context.ocr - DEBUG - Extracted 1 text elements
[Backend] 2025-07-09 23:03:23,749 - whispr.ContextService - INFO - OCR extracted 1 words (confidence: 1.00): 'Messages'
2025-07-09 23:03:23,749 - whispr.ContextService - DEBUG - High confidence words: ['Messages']
[Backend] 2025-07-09 23:03:23,750 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.32
2025-07-09 23:03:23,750 - whispr.ContextService - INFO - Active window: 'context_service.py - one.whispr - Cursor'
2025-07-09 23:03:23,750 - whispr.ContextService - INFO - Top keywords: ['messages (0.60)']
2025-07-09 23:03:23,750 - whispr.ContextService - INFO - Context relevance score: 0.32
[Backend] 2025-07-09 23:03:23,750 - whispr.context.cache - DEBUG - Stored context in cache: b8914471983996c32b806954dba4a841
2025-07-09 23:03:23,750 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.152s, OCR: 3.983s, Analysis: 0.000s
[Backend] 2025-07-09 23:03:23,751 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.32, processing time: 4.14s
[Backend] 2025-07-09 23:03:29,763 - whispr.ContextService - INFO - Context capture triggered: fallback timer
2025-07-09 23:03:29,763 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:03:29,773 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:03:29,903 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:03:29,912 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:03:29,916 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:03:33,134 - whispr.context.ocr - DEBUG - Extracted 2 text elements
[Backend] 2025-07-09 23:03:33,134 - whispr.ContextService - INFO - OCR extracted 2 words (confidence: 0.69): '76 Messages'
2025-07-09 23:03:33,134 - whispr.ContextService - DEBUG - High confidence words: ['Messages']
[Backend] 2025-07-09 23:03:33,135 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.28
2025-07-09 23:03:33,135 - whispr.ContextService - INFO - Active window: 'context_service.py - one.whispr - Cursor'
2025-07-09 23:03:33,135 - whispr.ContextService - INFO - Top keywords: ['messages (0.60)']
[Backend] 2025-07-09 23:03:33,135 - whispr.ContextService - DEBUG - Entities (numbers): ['76']
2025-07-09 23:03:33,135 - whispr.ContextService - INFO - Context relevance score: 0.28
2025-07-09 23:03:33,135 - whispr.context.cache - DEBUG - Stored context in cache: 158e48e489c46782f921bf3d5d083a00
2025-07-09 23:03:33,135 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.151s, OCR: 3.220s, Analysis: 0.001s
[Backend] 2025-07-09 23:03:33,136 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.28, processing time: 3.37s
[Backend] 2025-07-09 23:03:45,198 - whispr.ContextService - INFO - Context capture triggered: fallback timer
[Backend] 2025-07-09 23:03:45,198 - whispr.ContextService - DEBUG - Starting context capture
[Backend] 2025-07-09 23:03:45,211 - whispr.context.screen_capture - DEBUG - Failed to capture window content
[Backend] 2025-07-09 23:03:45,356 - whispr.context.screen_capture - DEBUG - Captured primary monitor: (3840, 2160)
[Backend] 2025-07-09 23:03:45,367 - whispr.helpers.context.image_processor - DEBUG - Smart resize: (3840, 2160) -> (1192, 670) (8,294,400 -> 798,640 pixels)
[Backend] 2025-07-09 23:03:45,372 - whispr.context.ocr - DEBUG - Processing image: (1192, 670), mode: RGB, array shape: (670, 1192, 3)
[Backend] 2025-07-09 23:03:47,396 - whispr.context.ocr - DEBUG - Extracted 2 text elements
[Backend] 2025-07-09 23:03:47,396 - whispr.ContextService - INFO - OCR extracted 2 words (confidence: 0.81): 'L3 Messages'
2025-07-09 23:03:47,396 - whispr.ContextService - DEBUG - High confidence words: ['Messages']
[Backend] 2025-07-09 23:03:47,397 - whispr.context.analyzer - DEBUG - Context analysis completed with relevance score: 0.28
2025-07-09 23:03:47,397 - whispr.ContextService - INFO - Active window: 'context_service.py - one.whispr - Cursor'
2025-07-09 23:03:47,397 - whispr.ContextService - INFO - Top keywords: ['messages (0.60)']
2025-07-09 23:03:47,397 - whispr.ContextService - INFO - Context relevance score: 0.28
2025-07-09 23:03:47,397 - whispr.context.cache - DEBUG - Stored context in cache: c36c3400752be70d45a0db7b7fbfe5e6
2025-07-09 23:03:47,397 - whispr.ContextService - DEBUG - Context processing times - Screenshot: 0.171s, OCR: 2.026s, Analysis: 0.001s
[Backend] 2025-07-09 23:03:47,398 - whispr.ContextService - INFO - Context captured successfully - relevance: 0.28, processing time: 2.20s
[Backend] 2025-07-09 23:03:47,680 - whispr.helpers.models.memory_manager - DEBUG - Moderate memory pressure - performing light cleanup
[Backend] 2025-07-09 23:03:47,684 - whispr.helpers.models.memory_manager - DEBUG - Starting memory cleanup (aggressive=False, light=True)
[Backend] 2025-07-09 23:03:47,778 - whispr.helpers.models.memory_manager - DEBUG - Python GC freed 256 objects
[Backend] 2025-07-09 23:03:47,879 - whispr.helpers.models.memory_manager - DEBUG - Memory cleanup completed in 0.20s, freed ~0.0MB