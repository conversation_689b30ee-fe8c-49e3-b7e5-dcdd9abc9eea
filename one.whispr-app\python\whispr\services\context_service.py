"""
Context awareness service for One.Whispr.

This service orchestrates context capture, processing, and provides API for other components.
It integrates screen capture, OCR, and context analysis to provide contextual information
that can enhance transcription accuracy.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any

from whispr.core.base import BaseService, ServiceContainer
from whispr.config.manager import ConfigurationMixin
from whispr.helpers.context import ScreenCaptureManager, OCRExtractor, ContextAnalyzer, ContextCache


class ContextService(BaseService, ConfigurationMixin):
    """Service for managing context awareness functionality."""
    
    def __init__(self, service_container: Optional[ServiceContainer] = None):
        """Initialize the context service.
        
        Args:
            service_container: The service container for dependency resolution
        """
        super().__init__(service_container)
        
        # Core components
        self._screen_capture = ScreenCaptureManager()
        self._ocr_extractor = OCRExtractor()
        self._context_analyzer = ContextAnalyzer()
        self._context_cache = ContextCache()
        
        # State
        self._is_enabled = False
        self._is_initialized = False
        self._last_capture_time = 0
        self._capture_in_progress = False
        
        # Configuration
        self._capture_mode = "primaryMonitor"
        self._max_image_size = (1920, 1080)
        self._ocr_languages = ['en']
        self._ocr_confidence_threshold = 0.5
        self._cache_ttl = 3600  # 1 hour
        
    async def initialize(self) -> bool:
        """Initialize the context service.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            # Call parent initialization
            result = await super().initialize()
            if not result:
                return False
            
            # Load settings to check if context awareness is enabled
            self._load_settings()
            
            if not self._is_enabled:
                self.logger.info("Context awareness is disabled in settings")
                return True
            
            # Initialize components
            if not self._screen_capture.initialize():
                self.logger.error("Failed to initialize screen capture")
                return False
            
            if not self._ocr_extractor.initialize(self._ocr_languages, self._ocr_confidence_threshold):
                self.logger.error("Failed to initialize OCR extractor")
                return False
            
            self._is_initialized = True
            self.logger.info("Context service initialized successfully")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing context service: {e}")
            return False
    
    async def cleanup(self) -> bool:
        """Clean up the context service.
        
        Returns:
            True if cleanup was successful, False otherwise
        """
        try:
            # Clean up components
            if self._screen_capture:
                self._screen_capture.cleanup()
            
            if self._ocr_extractor:
                self._ocr_extractor.cleanup()
            
            if self._context_cache:
                self._context_cache.clear_cache()
            
            self._is_initialized = False
            self.logger.info("Context service cleaned up successfully")
            
            return await super().cleanup()
            
        except Exception as e:
            self.logger.error(f"Error cleaning up context service: {e}")
            return False
    
    async def capture_context(self, force: bool = False) -> Optional[Dict[str, Any]]:
        """Capture current context (screenshot + OCR + analysis).
        
        Args:
            force: Force capture even if recently captured
            
        Returns:
            Context data or None if capture failed/disabled
        """
        if not self._is_enabled or not self._is_initialized:
            self.logger.debug("Context capture skipped - service disabled or not initialized")
            return None
        
        if self._capture_in_progress:
            self.logger.debug("Context capture already in progress")
            return None
        
        # Check if we captured recently (avoid spam)
        current_time = time.time()
        if not force and (current_time - self._last_capture_time) < 5.0:  # 5 second cooldown
            self.logger.debug("Context capture skipped - too recent")
            return None
        
        try:
            self._capture_in_progress = True
            self.logger.debug("Starting context capture")
            
            # Capture screenshot
            screenshot = self._screen_capture.capture_screen(self._capture_mode, self._max_image_size)
            if not screenshot:
                self.logger.warning("Failed to capture screenshot")
                return None
            
            # Extract text using OCR
            ocr_data = self._ocr_extractor.extract_text(screenshot)
            if not ocr_data or not ocr_data.get("text"):
                self.logger.debug("No text extracted from screenshot")
                # Still return context data even without text
                ocr_data = {"text": "", "words": [], "confidence": 0.0}
            
            # Get window title (placeholder - would need platform-specific implementation)
            window_title = self._get_active_window_title()
            
            # Analyze context
            context_analysis = self._context_analyzer.analyze_context(ocr_data, window_title)
            
            # Store in cache
            cache_key = self._context_cache.store_context(context_analysis, self._cache_ttl)
            
            # Update capture time
            self._last_capture_time = current_time
            
            # Add cache key to the analysis
            context_analysis["cache_key"] = cache_key
            
            self.logger.info(f"Context captured successfully - relevance: {context_analysis.get('relevance_score', 0.0):.2f}")
            
            return context_analysis
            
        except Exception as e:
            self.logger.error(f"Error capturing context: {e}")
            return None
        finally:
            self._capture_in_progress = False
    
    async def get_recent_context(self, limit: int = 5, min_relevance: float = 0.0) -> List[Dict[str, Any]]:
        """Get recent context data.
        
        Args:
            limit: Maximum number of contexts to return
            min_relevance: Minimum relevance score filter
            
        Returns:
            List of recent context data
        """
        if not self._is_enabled:
            return []
        
        try:
            return self._context_cache.get_recent_contexts(limit, min_relevance)
        except Exception as e:
            self.logger.error(f"Error getting recent context: {e}")
            return []
    
    async def search_context(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search for contexts containing specific text.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of matching context data
        """
        if not self._is_enabled or not query:
            return []
        
        try:
            return self._context_cache.search_contexts(query, limit)
        except Exception as e:
            self.logger.error(f"Error searching context: {e}")
            return []
    
    async def get_context_for_application(self, app_name: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get contexts related to a specific application.
        
        Args:
            app_name: Application name to filter by
            limit: Maximum number of results
            
        Returns:
            List of context data for the application
        """
        if not self._is_enabled or not app_name:
            return []
        
        try:
            return self._context_cache.get_contexts_by_application(app_name, limit)
        except Exception as e:
            self.logger.error(f"Error getting context for application: {e}")
            return []
    
    def update_settings(self) -> None:
        """Update service settings from configuration."""
        self._load_settings()
        
        if self._is_enabled and not self._is_initialized:
            # Context awareness was enabled, try to initialize
            asyncio.create_task(self.initialize())
        elif not self._is_enabled and self._is_initialized:
            # Context awareness was disabled, cleanup
            asyncio.create_task(self.cleanup())
    
    def get_status(self) -> Dict[str, Any]:
        """Get the current service status.
        
        Returns:
            A dictionary containing status information
        """
        status = super().get_status()
        
        cache_stats = {}
        if self._context_cache:
            cache_stats = self._context_cache.get_cache_stats()
        
        status.update({
            "enabled": self._is_enabled,
            "initialized": self._is_initialized,
            "capture_in_progress": self._capture_in_progress,
            "last_capture_time": self._last_capture_time,
            "screen_capture_ready": self._screen_capture.is_initialized() if self._screen_capture else False,
            "ocr_ready": self._ocr_extractor.is_initialized() if self._ocr_extractor else False,
            "cache_stats": cache_stats,
            "settings": {
                "capture_mode": self._capture_mode,
                "max_image_size": self._max_image_size,
                "ocr_languages": self._ocr_languages,
                "ocr_confidence_threshold": self._ocr_confidence_threshold
            }
        })
        
        return status
    
    def _load_settings(self) -> None:
        """Load settings from configuration."""
        try:
            settings = self.get_settings()

            # Check if context awareness is enabled
            self._is_enabled = settings.get("contextAwareness", False)

            # Load technical configuration from config manager
            config_manager = self.get_service("config")
            context_config = config_manager.get("context") if config_manager else {}

            # Load configuration with defaults from settings.py
            self._capture_mode = context_config.get("default_capture_mode", "primaryMonitor")
            self._max_image_size = tuple(context_config.get("default_max_image_size", (1920, 1080)))
            self._ocr_languages = context_config.get("default_ocr_languages", ['en'])
            self._ocr_confidence_threshold = context_config.get("default_ocr_confidence_threshold", 0.5)
            self._cache_ttl = context_config.get("default_cache_ttl", 3600)

            self.logger.debug(f"Context awareness settings loaded - enabled: {self._is_enabled}")

        except Exception as e:
            self.logger.error(f"Error loading context settings: {e}")
            self._is_enabled = False
    
    def _get_active_window_title(self) -> Optional[str]:
        """Get the title of the active window.
        
        This is a placeholder implementation. A full implementation would use
        platform-specific APIs (e.g., pywin32 on Windows) to get the actual
        active window title.
        
        Returns:
            Active window title or None
        """
        # TODO: Implement platform-specific window title detection
        # For Windows: use pywin32 GetForegroundWindow() and GetWindowText()
        # For macOS: use Quartz or AppKit
        # For Linux: use X11 or Wayland APIs
        return None
