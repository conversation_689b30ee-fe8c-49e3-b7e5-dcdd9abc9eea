"""
Context analysis functionality for processing extracted text and identifying
relevant contextual information for transcription enhancement.
"""

import logging
import re
import time
from typing import Dict, List, Optional, Set, Any
from collections import Counter


class ContextAnalyzer:
    """Analyzes extracted context data to identify relevant information."""
    
    def __init__(self):
        """Initialize the context analyzer."""
        self.logger = logging.getLogger("whispr.context.analyzer")
        self._application_patterns = self._build_application_patterns()
        self._technical_patterns = self._build_technical_patterns()
        
    def analyze_context(self, ocr_data: Dict[str, Any], window_title: Optional[str] = None) -> Dict[str, Any]:
        """Analyze context data and extract relevant information.
        
        Args:
            ocr_data: OCR extraction results
            window_title: Optional window title for additional context
            
        Returns:
            Analyzed context data with categorized information
        """
        try:
            text = ocr_data.get("text", "")
            words = ocr_data.get("words", [])

            # Debug logging to understand what OCR is providing
            word_count = len(words) if words else 0
            self.logger.debug(f"Context analysis input: {word_count} words, text length: {len(text)}")
            if words:
                sample_words = [w.get("text", "") for w in words[:5]]  # First 5 words
                self.logger.debug(f"Sample words from OCR: {sample_words}")

            analysis = {
                "timestamp": time.time(),
                "raw_text": text,
                "word_count": len(words),
                "confidence": ocr_data.get("confidence", 0.0),
                "applications": self._detect_applications(text, window_title),
                "technical_terms": self._extract_technical_terms(text),
                "keywords": self._extract_context_keywords(text),
                "entities": self._extract_entities(text),
                "window_context": self._analyze_window_context(window_title) if window_title else {},
                "relevance_score": 0.0
            }
            
            # Calculate relevance score
            analysis["relevance_score"] = self._calculate_relevance_score(analysis)
            
            self.logger.debug(f"Context analysis completed with relevance score: {analysis['relevance_score']:.2f}")
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing context: {e}")
            return self._empty_analysis()
    
    def _detect_applications(self, text: str, window_title: Optional[str] = None) -> List[Dict[str, Any]]:
        """Detect applications and software mentioned in the text.
        
        Args:
            text: Text to analyze
            window_title: Optional window title
            
        Returns:
            List of detected applications with confidence scores
        """
        applications = []
        text_lower = text.lower()
        
        # Check window title first
        if window_title:
            for app_name, patterns in self._application_patterns.items():
                if any(pattern in window_title.lower() for pattern in patterns):
                    applications.append({
                        "name": app_name,
                        "confidence": 0.9,
                        "source": "window_title"
                    })
        
        # Check text content
        for app_name, patterns in self._application_patterns.items():
            matches = sum(1 for pattern in patterns if pattern in text_lower)
            if matches > 0:
                confidence = min(0.8, matches * 0.3)  # Max 0.8 for text matches
                applications.append({
                    "name": app_name,
                    "confidence": confidence,
                    "source": "text_content"
                })
        
        # Remove duplicates and sort by confidence
        unique_apps = {}
        for app in applications:
            name = app["name"]
            if name not in unique_apps or app["confidence"] > unique_apps[name]["confidence"]:
                unique_apps[name] = app
        
        return sorted(unique_apps.values(), key=lambda x: x["confidence"], reverse=True)
    
    def _extract_technical_terms(self, text: str) -> List[Dict[str, Any]]:
        """Extract technical terms and jargon from the text.
        
        Args:
            text: Text to analyze
            
        Returns:
            List of technical terms with categories
        """
        technical_terms = []
        text_lower = text.lower()
        
        for category, patterns in self._technical_patterns.items():
            for pattern in patterns:
                matches = re.finditer(r'\b' + re.escape(pattern) + r'\b', text_lower)
                for match in matches:
                    technical_terms.append({
                        "term": pattern,
                        "category": category,
                        "position": match.start()
                    })
        
        # Remove duplicates
        unique_terms = {}
        for term in technical_terms:
            key = (term["term"], term["category"])
            if key not in unique_terms:
                unique_terms[key] = term
        
        return list(unique_terms.values())
    
    def _extract_context_keywords(self, text: str) -> List[Dict[str, Any]]:
        """Extract contextually relevant keywords.
        
        Args:
            text: Text to analyze
            
        Returns:
            List of keywords with relevance scores
        """
        if not text:
            return []
            
        # Extract words
        words = re.findall(r'\b[a-zA-Z][a-zA-Z0-9]*\b', text.lower())
        
        # Filter and score words
        keyword_scores = {}
        for word in words:
            if self._is_relevant_keyword(word):
                score = self._calculate_keyword_score(word, text)
                if score > 0.1:  # Minimum relevance threshold
                    keyword_scores[word] = score
        
        # Convert to list and sort by score
        keywords = [
            {"keyword": word, "score": score}
            for word, score in keyword_scores.items()
        ]
        
        return sorted(keywords, key=lambda x: x["score"], reverse=True)[:15]
    
    def _extract_entities(self, text: str) -> Dict[str, List[str]]:
        """Extract named entities from the text.
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary of entity types and their values
        """
        entities = {
            "emails": [],
            "urls": [],
            "file_paths": [],
            "numbers": [],
            "dates": []
        }
        
        # Email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        entities["emails"] = re.findall(email_pattern, text)
        
        # URLs
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        entities["urls"] = re.findall(url_pattern, text)
        
        # File paths (Windows and Unix style)
        file_pattern = r'[A-Za-z]:\\[^\s<>"|?*]+|/[^\s<>"|?*]+'
        entities["file_paths"] = re.findall(file_pattern, text)
        
        # Numbers (including decimals)
        number_pattern = r'\b\d+(?:\.\d+)?\b'
        entities["numbers"] = re.findall(number_pattern, text)
        
        # Simple date patterns
        date_pattern = r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b'
        entities["dates"] = re.findall(date_pattern, text)
        
        return entities
    
    def _analyze_window_context(self, window_title: str) -> Dict[str, Any]:
        """Analyze window title for additional context.
        
        Args:
            window_title: Window title to analyze
            
        Returns:
            Window context information
        """
        if not window_title:
            return {}
            
        return {
            "title": window_title,
            "application": self._extract_app_from_title(window_title),
            "document_name": self._extract_document_name(window_title),
            "is_browser": "browser" in window_title.lower() or any(
                browser in window_title.lower() 
                for browser in ["chrome", "firefox", "edge", "safari"]
            )
        }
    
    def _calculate_relevance_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate overall relevance score for the context.
        
        Args:
            analysis: Context analysis data
            
        Returns:
            Relevance score between 0.0 and 1.0
        """
        score = 0.0
        
        # Base score from OCR confidence
        score += analysis.get("confidence", 0.0) * 0.2
        
        # Application detection bonus
        apps = analysis.get("applications", [])
        if apps:
            max_app_confidence = max(app["confidence"] for app in apps)
            score += max_app_confidence * 0.3
        
        # Technical terms bonus
        tech_terms = analysis.get("technical_terms", [])
        score += min(len(tech_terms) * 0.05, 0.2)
        
        # Keywords bonus
        keywords = analysis.get("keywords", [])
        if keywords:
            avg_keyword_score = sum(kw["score"] for kw in keywords) / len(keywords)
            score += avg_keyword_score * 0.2
        
        # Entities bonus
        entities = analysis.get("entities", {})
        entity_count = sum(len(entity_list) for entity_list in entities.values())
        score += min(entity_count * 0.02, 0.1)
        
        return min(score, 1.0)
    
    def _build_application_patterns(self) -> Dict[str, List[str]]:
        """Build patterns for application detection."""
        return {
            "Microsoft Word": ["word", "microsoft word", "winword"],
            "Microsoft Excel": ["excel", "microsoft excel"],
            "Microsoft PowerPoint": ["powerpoint", "microsoft powerpoint"],
            "Google Chrome": ["chrome", "google chrome"],
            "Mozilla Firefox": ["firefox", "mozilla"],
            "Visual Studio Code": ["visual studio code", "vscode", "code"],
            "Notepad++": ["notepad++", "notepad plus"],
            "Adobe Acrobat": ["acrobat", "adobe acrobat", "pdf"],
            "Slack": ["slack"],
            "Discord": ["discord"],
            "Zoom": ["zoom"],
            "Teams": ["microsoft teams", "teams"],
            "Outlook": ["outlook", "microsoft outlook"]
        }
    
    def _build_technical_patterns(self) -> Dict[str, List[str]]:
        """Build patterns for technical term detection."""
        return {
            "programming": [
                "function", "variable", "class", "method", "array", "object",
                "string", "integer", "boolean", "null", "undefined", "return",
                "import", "export", "const", "let", "var", "if", "else", "for",
                "while", "try", "catch", "async", "await", "promise"
            ],
            "web": [
                "html", "css", "javascript", "react", "vue", "angular",
                "node", "npm", "webpack", "api", "rest", "json", "xml",
                "http", "https", "url", "domain", "server", "client"
            ],
            "database": [
                "sql", "mysql", "postgresql", "mongodb", "database", "table",
                "query", "select", "insert", "update", "delete", "join"
            ],
            "system": [
                "windows", "linux", "macos", "terminal", "command", "shell",
                "bash", "powershell", "registry", "service", "process"
            ]
        }
    
    def _is_relevant_keyword(self, word: str) -> bool:
        """Check if a word is relevant as a keyword."""
        if len(word) < 3:
            return False
            
        # Skip common stop words
        stop_words = {
            "the", "and", "for", "are", "but", "not", "you", "all", "can", "had",
            "her", "was", "one", "our", "out", "day", "get", "has", "him", "his",
            "how", "man", "new", "now", "old", "see", "two", "way", "who", "boy",
            "did", "its", "let", "put", "say", "she", "too", "use"
        }
        
        return word not in stop_words and not word.isdigit()
    
    def _calculate_keyword_score(self, word: str, text: str) -> float:
        """Calculate relevance score for a keyword."""
        # Base score
        score = 0.3
        
        # Length bonus (longer words are often more specific)
        if len(word) > 6:
            score += 0.2
        elif len(word) > 4:
            score += 0.1
        
        # Frequency penalty (very common words are less relevant)
        frequency = text.lower().count(word)
        if frequency > 3:
            score -= 0.2
        
        # Capitalization bonus (proper nouns are often important)
        if any(word.capitalize() in text for word in [word]):
            score += 0.1
        
        return max(0.0, min(1.0, score))
    
    def _extract_app_from_title(self, title: str) -> Optional[str]:
        """Extract application name from window title."""
        # Common patterns for extracting app names
        if " - " in title:
            return title.split(" - ")[-1]
        elif " | " in title:
            return title.split(" | ")[-1]
        return None
    
    def _extract_document_name(self, title: str) -> Optional[str]:
        """Extract document name from window title."""
        if " - " in title:
            return title.split(" - ")[0]
        elif " | " in title:
            return title.split(" | ")[0]
        return title
    
    def _empty_analysis(self) -> Dict[str, Any]:
        """Return empty analysis structure."""
        return {
            "timestamp": time.time(),
            "raw_text": "",
            "word_count": 0,
            "confidence": 0.0,
            "applications": [],
            "technical_terms": [],
            "keywords": [],
            "entities": {},
            "window_context": {},
            "relevance_score": 0.0
        }
