"""
Image processing utilities for context awareness.

This module handles image optimization, caching, and preprocessing for OCR.
"""

import hashlib
import logging
import time
from typing import Optional, Dict, Any, Tuple
from PIL import Image


class ImageProcessor:
    """Handles image processing and caching for context awareness."""
    
    def __init__(self):
        """Initialize the image processor."""
        self.logger = logging.getLogger(__name__)
        self._last_image_hash = None
        self._last_ocr_result = None
        
    def optimize_for_ocr(self, image: Image.Image) -> Image.Image:
        """Optimize image for faster OCR processing.
        
        Args:
            image: Original PIL Image
            
        Returns:
            Optimized PIL Image
        """
        try:
            original_size = image.size

            # Early exit for extremely large images that would be very slow
            if max(image.size) > 4000:
                self.logger.warning(f"Image too large ({original_size}), may be slow to process")

            # Smart resolution scaling for optimal OCR speed vs quality
            target_pixels = 800000  # ~800K pixels (e.g., 1000x800) for good speed/quality balance
            current_pixels = image.width * image.height

            if current_pixels > target_pixels:
                # Calculate scale to reach target pixel count
                scale = (target_pixels / current_pixels) ** 0.5
                new_width = int(image.width * scale)
                new_height = int(image.height * scale)

                # Ensure minimum readable size
                if new_width < 640 or new_height < 480:
                    new_width = max(640, new_width)
                    new_height = max(480, new_height)

                # Use fastest resampling for speed
                image = image.resize((new_width, new_height), Image.Resampling.NEAREST)
                self.logger.debug(f"Smart resize: {original_size} -> {image.size} ({current_pixels:,} -> {new_width*new_height:,} pixels)")
            
            # Convert to RGB for better OCR compatibility
            if image.mode != 'RGB':
                image = image.convert('RGB')
                self.logger.debug("Converted image to RGB for OCR processing")

            # Optional: Crop to center region for faster processing (most text is usually in center)
            # This can significantly speed up OCR by focusing on the most relevant area
            if image.width > 1200 and image.height > 800:
                # Crop to center 80% of the image
                crop_margin_x = int(image.width * 0.1)
                crop_margin_y = int(image.height * 0.1)

                cropped = image.crop((
                    crop_margin_x,
                    crop_margin_y,
                    image.width - crop_margin_x,
                    image.height - crop_margin_y
                ))

                self.logger.debug(f"Cropped to center region: {image.size} -> {cropped.size}")
                image = cropped

            return image
            
        except Exception as e:
            self.logger.error(f"Error optimizing image for OCR: {e}")
            return image
    
    def calculate_image_hash(self, image: Image.Image) -> str:
        """Calculate a hash of the image for caching purposes.
        
        Args:
            image: PIL Image to hash
            
        Returns:
            Hash string of the image
        """
        try:
            # Resize to small size for fast hashing
            hash_image = image.resize((32, 32), Image.Resampling.NEAREST)
            # Convert to bytes and hash
            image_bytes = hash_image.tobytes()
            return hashlib.md5(image_bytes).hexdigest()
        except Exception as e:
            self.logger.error(f"Error calculating image hash: {e}")
            return str(time.time())  # Fallback to timestamp
    
    def check_cache(self, image_hash: str) -> Optional[Dict[str, Any]]:
        """Check if we have cached OCR results for this image.
        
        Args:
            image_hash: Hash of the image
            
        Returns:
            Cached OCR result or None
        """
        if image_hash == self._last_image_hash and self._last_ocr_result:
            self.logger.debug("Image identical to previous capture, reusing OCR result")
            return self._last_ocr_result
        return None
    
    def cache_result(self, image_hash: str, ocr_result: Dict[str, Any]) -> None:
        """Cache OCR result for future use.
        
        Args:
            image_hash: Hash of the image
            ocr_result: OCR result to cache
        """
        self._last_image_hash = image_hash
        self._last_ocr_result = ocr_result
    
    def is_image_too_small(self, image: Image.Image) -> bool:
        """Check if image is too small for meaningful OCR.
        
        Args:
            image: PIL Image to check
            
        Returns:
            True if image is too small
        """
        return image.size[0] < 200 or image.size[1] < 100
    
    def clear_cache(self) -> None:
        """Clear the image cache."""
        self._last_image_hash = None
        self._last_ocr_result = None
        self.logger.debug("Image processor cache cleared")
