"""
Command handlers for <PERSON>.Whispr.

This package contains command handlers for various features.
"""

import logging
from typing import Any

logger = logging.getLogger("whispr.handlers")


def register_all_handlers(handler_manager: Any) -> None:
    """Register all handlers with the handler manager.
    
    Args:
        handler_manager: The command handler manager
    """
    # Import handler modules
    from whispr.handlers.audio_handlers import register_handlers as register_audio_handlers
    from whispr.handlers.transcription_handlers import register_handlers as register_transcription_handlers
    from whispr.handlers.shortcut_handlers import register_handlers as register_shortcut_handlers
    from whispr.handlers.system_handlers import register_handlers as register_system_handlers
    from whispr.handlers.database_handlers import register_handlers as register_database_handlers
    from whispr.handlers.model_handlers import register_handlers as register_model_handlers
    from whispr.handlers.context_handlers import register_context_handlers

    # Register all handlers
    register_audio_handlers(handler_manager)
    register_transcription_handlers(handler_manager)
    register_shortcut_handlers(handler_manager)
    register_system_handlers(handler_manager)
    register_database_handlers(handler_manager)
    register_model_handlers(handler_manager)
    register_context_handlers(handler_manager.registry.handlers, handler_manager.service_container)
    
    # Register event subscription handler
    register_event_subscription_handlers(handler_manager)
    
    logger.info("All handlers registered successfully")


def register_event_subscription_handlers(handler_manager: Any) -> None:
    """Register event subscription handlers.
    
    Args:
        handler_manager: The command handler manager
    """
    logger.debug("Registering event subscription handlers")
    
    # Get the IPC bridge service
    service_container = handler_manager.service_container
    ipc_bridge = service_container.resolve("ipc") if service_container else None
    
    if ipc_bridge and hasattr(ipc_bridge, '_handle_subscribe_events'):
        # Register the subscribe_events handler
        handler_manager.registry.register_function(
            "events.subscribe", 
            ipc_bridge._handle_subscribe_events
        )
        
        logger.info("Event subscription handlers registered successfully")
    else:
        logger.warning("Could not register event subscription handlers: IPC bridge not available or missing handler method") 