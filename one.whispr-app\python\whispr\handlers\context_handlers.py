"""
Context awareness handlers for WebSocket API.

This module provides handlers for context-related WebSocket requests including
context capture, retrieval, search, and configuration operations.
"""

import logging
from typing import Dict, Any, Optional

from whispr.core.base import ServiceContainer
from whispr.core.response_utils import success_response, error_response


class ContextHandlers:
    """Handlers for context awareness WebSocket requests."""
    
    def __init__(self, service_container: ServiceContainer):
        """Initialize context handlers.
        
        Args:
            service_container: The service container for dependency resolution
        """
        self.service_container = service_container
        self.logger = logging.getLogger("whispr.handlers.context")
    
    async def handle_capture_context(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle context capture request.
        
        Args:
            data: Request data containing capture parameters
            
        Returns:
            Response with captured context data or error
        """
        try:
            context_service = self.service_container.resolve("context")
            if not context_service:
                return error_response("Context service not available")

            # Get parameters
            force = data.get("force", False)

            # Capture context
            context_data = await context_service.capture_context(force=force)

            if context_data:
                return success_response({
                    "context": context_data,
                    "message": "Context captured successfully"
                })
            else:
                return error_response("Failed to capture context or context disabled")

        except Exception as e:
            self.logger.error(f"Error handling capture context request: {e}")
            return error_response(f"Error capturing context: {str(e)}")
    
    async def handle_get_recent_context(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle request for recent context data.
        
        Args:
            data: Request data containing query parameters
            
        Returns:
            Response with recent context data or error
        """
        try:
            context_service = self.service_container.resolve("context")
            if not context_service:
                return error_response("Context service not available")

            # Get parameters
            limit = data.get("limit", 5)
            min_relevance = data.get("minRelevance", 0.0)

            # Validate parameters
            if not isinstance(limit, int) or limit < 1 or limit > 50:
                return error_response("Invalid limit parameter (must be 1-50)")

            if not isinstance(min_relevance, (int, float)) or min_relevance < 0.0 or min_relevance > 1.0:
                return error_response("Invalid minRelevance parameter (must be 0.0-1.0)")

            # Get recent contexts
            contexts = await context_service.get_recent_context(limit, min_relevance)

            return success_response({
                "contexts": contexts,
                "count": len(contexts),
                "message": f"Retrieved {len(contexts)} recent contexts"
            })

        except Exception as e:
            self.logger.error(f"Error handling get recent context request: {e}")
            return error_response(f"Error retrieving recent contexts: {str(e)}")
    
    async def handle_search_context(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle context search request.
        
        Args:
            data: Request data containing search parameters
            
        Returns:
            Response with search results or error
        """
        try:
            context_service = self.service_container.resolve("context")
            if not context_service:
                return error_response("Context service not available")

            # Get parameters
            query = data.get("query", "").strip()
            limit = data.get("limit", 5)

            # Validate parameters
            if not query:
                return error_response("Query parameter is required")

            if not isinstance(limit, int) or limit < 1 or limit > 20:
                return error_response("Invalid limit parameter (must be 1-20)")

            # Search contexts
            contexts = await context_service.search_context(query, limit)

            return success_response({
                "contexts": contexts,
                "query": query,
                "count": len(contexts),
                "message": f"Found {len(contexts)} contexts matching '{query}'"
            })

        except Exception as e:
            self.logger.error(f"Error handling search context request: {e}")
            return error_response(f"Error searching contexts: {str(e)}")
    
    async def handle_get_context_by_application(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle request for contexts by application.
        
        Args:
            data: Request data containing application parameters
            
        Returns:
            Response with application contexts or error
        """
        try:
            context_service = self.service_container.resolve("context")
            if not context_service:
                return error_response("Context service not available")
            
            # Get parameters
            app_name = data.get("appName", "").strip()
            limit = data.get("limit", 5)
            
            # Validate parameters
            if not app_name:
                return error_response("appName parameter is required")
            
            if not isinstance(limit, int) or limit < 1 or limit > 20:
                return error_response("Invalid limit parameter (must be 1-20)")

            # Get contexts for application
            contexts = await context_service.get_context_for_application(app_name, limit)

            return success_response({
                "contexts": contexts,
                "appName": app_name,
                "count": len(contexts),
                "message": f"Found {len(contexts)} contexts for application '{app_name}'"
            })

        except Exception as e:
            self.logger.error(f"Error handling get context by application request: {e}")
            return error_response(f"Error retrieving application contexts: {str(e)}")
    
    async def handle_get_context_status(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle context service status request.
        
        Args:
            data: Request data (unused)
            
        Returns:
            Response with context service status
        """
        try:
            context_service = self.service_container.resolve("context")
            if not context_service:
                return error_response("Context service not available")

            # Get service status
            status = context_service.get_status()

            return success_response({
                "status": status,
                "message": "Context service status retrieved successfully"
            })

        except Exception as e:
            self.logger.error(f"Error handling get context status request: {e}")
            return error_response(f"Error retrieving context status: {str(e)}")
    
    async def handle_update_context_settings(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle context settings update request.
        
        Args:
            data: Request data containing settings updates
            
        Returns:
            Response confirming settings update
        """
        try:
            context_service = self.service_container.resolve("context")
            if not context_service:
                return create_error_response("Context service not available")
            
            # Update settings (this will reload from the database)
            context_service.update_settings()
            
            # Get updated status
            status = context_service.get_status()
            
            return create_success_response({
                "status": status,
                "message": "Context settings updated successfully"
            })
            
        except Exception as e:
            self.logger.error(f"Error handling update context settings request: {e}")
            return create_error_response(f"Error updating context settings: {str(e)}")
    
    async def handle_clear_context_cache(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle context cache clear request.
        
        Args:
            data: Request data (unused)
            
        Returns:
            Response confirming cache clear
        """
        try:
            context_service = self.service_container.resolve("context")
            if not context_service:
                return create_error_response("Context service not available")
            
            # Clear cache through the service
            if hasattr(context_service, '_context_cache') and context_service._context_cache:
                context_service._context_cache.clear_cache()
                
                return create_success_response({
                    "message": "Context cache cleared successfully"
                })
            else:
                return create_error_response("Context cache not available")
            
        except Exception as e:
            self.logger.error(f"Error handling clear context cache request: {e}")
            return create_error_response(f"Error clearing context cache: {str(e)}")


# Handler registration function
def register_context_handlers(handlers_dict: Dict[str, Any], service_container: ServiceContainer) -> None:
    """Register context handlers with the handlers dictionary.
    
    Args:
        handlers_dict: Dictionary to register handlers in
        service_container: Service container for dependency resolution
    """
    context_handlers = ContextHandlers(service_container)
    
    # Register all context-related handlers
    handlers_dict.update({
        "context:capture": context_handlers.handle_capture_context,
        "context:getRecent": context_handlers.handle_get_recent_context,
        "context:search": context_handlers.handle_search_context,
        "context:getByApplication": context_handlers.handle_get_context_by_application,
        "context:getStatus": context_handlers.handle_get_context_status,
        "context:updateSettings": context_handlers.handle_update_context_settings,
        "context:clearCache": context_handlers.handle_clear_context_cache,
    })
