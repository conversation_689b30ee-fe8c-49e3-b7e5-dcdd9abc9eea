"""
OCR text extraction functionality for context awareness.

Uses EasyOCR library to extract text from screenshots with confidence filtering
and text preprocessing capabilities.
"""

import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image
import numpy as np
import easyocr


class OCRExtractor:
    """Manages OCR text extraction from images."""
    
    def __init__(self):
        """Initialize the OCR extractor."""
        self.logger = logging.getLogger("whispr.context.ocr")
        self._reader = None
        self._languages = ['en']  # Default to English
        self._confidence_threshold = 0.5
        self._initialized = False
        
    def initialize(self, languages: Optional[List[str]] = None, confidence_threshold: float = 0.5) -> bool:
        """Initialize the OCR system.
        
        Args:
            languages: List of language codes (e.g., ['en', 'es', 'fr'])
            confidence_threshold: Minimum confidence threshold for text detection (0.0 to 1.0)
            
        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            if languages:
                self._languages = languages
            self._confidence_threshold = confidence_threshold
            
            self.logger.info(f"Initializing EasyOCR with languages: {self._languages}")
            # Try GPU first, fall back to CPU if CUDA not available
            try:
                self._reader = easyocr.Reader(self._languages, gpu=True)
                self.logger.info("EasyOCR initialized with GPU acceleration")
            except Exception as e:
                self.logger.warning(f"GPU initialization failed, falling back to CPU: {e}")
                self._reader = easyocr.Reader(self._languages, gpu=False)
            
            self._initialized = True
            self.logger.info("OCR extractor initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize OCR extractor: {e}")
            return False
    
    def extract_text(self, image: Image.Image) -> Dict[str, Any]:
        """Extract text from an image.
        
        Args:
            image: PIL Image object to extract text from
            
        Returns:
            Dictionary containing extracted text and metadata
        """
        if not self._initialized or not self._reader:
            self.logger.error("OCR extractor not initialized")
            return {"text": "", "words": [], "confidence": 0.0}
            
        try:
            # Convert PIL Image to numpy array
            image_array = np.array(image)

            # Log image properties for debugging
            self.logger.debug(f"Processing image: {image.size}, mode: {image.mode}, array shape: {image_array.shape}")
            
            # Perform OCR with aggressive speed optimizations
            results = self._reader.readtext(
                image_array,
                width_ths=0.8,  # Higher threshold for faster processing
                height_ths=0.8,  # Higher threshold for faster processing
                paragraph=False,  # Disable paragraph detection for speed
                batch_size=1,  # Process one at a time for lower memory usage
                detail=0,  # Return only text without bounding boxes for speed
                allowlist=None,  # No character restrictions for speed
                blocklist=None,  # No character blocks for speed
                min_size=10,  # Skip very small text for speed
                text_threshold=0.6,  # Lower text confidence threshold for speed
                low_text=0.3,  # Lower link threshold for speed
                link_threshold=0.3,  # Lower link threshold for speed
                canvas_size=1280,  # Smaller canvas for faster processing
                mag_ratio=1.0  # No magnification for speed
            )
            
            # Process results
            extracted_data = self._process_ocr_results(results)
            
            self.logger.debug(f"Extracted {len(extracted_data['words'])} text elements")
            return extracted_data
            
        except Exception as e:
            self.logger.error(f"Error extracting text from image: {e}")
            return {"text": "", "words": [], "confidence": 0.0}
    
    def _process_ocr_results(self, results: List[Tuple]) -> Dict[str, Any]:
        """Process raw OCR results into structured data.
        
        Args:
            results: Raw OCR results from EasyOCR
            
        Returns:
            Processed text data with confidence filtering
        """
        words = []
        all_text = []
        total_confidence = 0.0
        valid_results = 0
        
        for (bbox, text, confidence) in results:
            # Filter by confidence threshold
            if confidence >= self._confidence_threshold:
                # Clean up the text
                cleaned_text = self._clean_text(text)
                
                if cleaned_text:  # Only include non-empty text
                    words.append({
                        "text": cleaned_text,
                        "confidence": confidence,
                        "bbox": bbox
                    })
                    all_text.append(cleaned_text)
                    total_confidence += confidence
                    valid_results += 1
        
        # Calculate average confidence
        avg_confidence = total_confidence / valid_results if valid_results > 0 else 0.0
        
        # Join all text with spaces
        full_text = " ".join(all_text)
        
        return {
            "text": full_text,
            "words": words,
            "confidence": avg_confidence,
            "word_count": len(words)
        }
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text.
        
        Args:
            text: Raw text from OCR
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
            
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove very short "words" that are likely OCR noise
        if len(text) < 2:
            return ""
            
        # Remove text that's mostly special characters
        if len(re.sub(r'[^a-zA-Z0-9\s]', '', text)) < len(text) * 0.3:
            return ""
            
        return text
    
    def extract_keywords(self, text: str, min_length: int = 3) -> List[str]:
        """Extract potential keywords from text.
        
        Args:
            text: Text to extract keywords from
            min_length: Minimum length for keywords
            
        Returns:
            List of potential keywords
        """
        if not text:
            return []
            
        try:
            # Split into words and filter
            words = re.findall(r'\b[a-zA-Z0-9]+\b', text.lower())
            
            # Filter by length and common stop words
            stop_words = {
                'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
                'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
                'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those',
                'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
                'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
                'can', 'must', 'shall', 'a', 'an', 'as', 'if', 'when', 'where', 'why',
                'how', 'what', 'which', 'who', 'whom', 'whose', 'all', 'any', 'both',
                'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor',
                'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very'
            }
            
            keywords = []
            for word in words:
                if (len(word) >= min_length and 
                    word not in stop_words and 
                    not word.isdigit()):
                    keywords.append(word)
            
            # Remove duplicates while preserving order
            unique_keywords = []
            seen = set()
            for keyword in keywords:
                if keyword not in seen:
                    unique_keywords.append(keyword)
                    seen.add(keyword)
            
            return unique_keywords[:20]  # Limit to top 20 keywords
            
        except Exception as e:
            self.logger.error(f"Error extracting keywords: {e}")
            return []
    
    def set_confidence_threshold(self, threshold: float) -> None:
        """Set the confidence threshold for text detection.
        
        Args:
            threshold: New confidence threshold (0.0 to 1.0)
        """
        if 0.0 <= threshold <= 1.0:
            self._confidence_threshold = threshold
            self.logger.debug(f"OCR confidence threshold set to {threshold}")
        else:
            self.logger.warning(f"Invalid confidence threshold: {threshold}")
    
    def set_languages(self, languages: List[str]) -> bool:
        """Set the languages for OCR detection.
        
        Args:
            languages: List of language codes
            
        Returns:
            True if languages were set successfully, False otherwise
        """
        try:
            if languages != self._languages:
                self._languages = languages
                # Reinitialize reader with new languages
                self.logger.info(f"Reinitializing OCR with new languages: {languages}")
                self._reader = easyocr.Reader(self._languages, gpu=False)
                self.logger.info("OCR languages updated successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error setting OCR languages: {e}")
            return False
    
    def is_initialized(self) -> bool:
        """Check if the OCR extractor is initialized.
        
        Returns:
            True if initialized, False otherwise
        """
        return self._initialized and self._reader is not None
    
    def cleanup(self) -> None:
        """Clean up OCR resources."""
        self._reader = None
        self._initialized = False
        self.logger.debug("OCR extractor resources cleaned up")
