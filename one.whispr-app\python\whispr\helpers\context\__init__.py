"""
Context awareness helpers for One.Whispr.

This module provides context awareness functionality including:
- Screen capture using MSS
- OCR text extraction using EasyOCR
- Context analysis and keyword extraction
- Context caching and management

The context system captures screenshots and extracts text when recording starts
to provide contextual information that can enhance transcription accuracy.
"""

from .screen_capture import ScreenCaptureManager
from .ocr_extractor import OCRExtractor
from .context_analyzer import ContextAnalyzer
from .context_cache import ContextCache

__all__ = [
    'ScreenCaptureManager',
    'OCRExtractor', 
    'ContextAnalyzer',
    'ContextCache'
]
